# RCC Multiple Behavior Selection System

## Overview
This update adds the ability to select multiple behaviors in RCC (Realistic Car Controller) and apply them individually to vehicles. Previously, only one behavior could be selected globally. Now you can:

1. Select multiple behaviors from the available list
2. Apply different behaviors to individual vehicles
3. Switch between single and multiple selection modes
4. Use UI components to manage behavior selection

## Key Features

### 1. Multiple Behavior Selection
- Select multiple behaviors simultaneously from RCC Settings
- Each selected behavior remains available for application
- Toggle between single and multiple selection modes

### 2. Individual Vehicle Behavior
- Each vehicle can have its own behavior setting
- Override global behavior settings per vehicle
- Apply specific behaviors to specific vehicles

### 3. Enhanced UI Components
- `RCC_UI_BehaviorSelector`: Complete UI for behavior management
- `RCC_UI_BehaviorButton`: Individual behavior selection buttons
- `RCC_MultipleBehaviorDemo`: Demo script showing usage

## How to Use

### In RCC Settings (Editor)
1. Open RCC Settings (Tools > BoneCracker Games > Realistic Car Controller > RCC Settings)
2. In Behavior Settings section, check "Use Multiple Behavior Selection"
3. Select multiple behaviors by checking the checkboxes
4. Selected behaviors will be available for individual vehicle assignment

### In Code
```csharp
// Add behavior to multiple selection
RCC.AddBehaviorToMultipleSelection(behaviorIndex);

// Remove behavior from multiple selection
RCC.RemoveBehaviorFromMultipleSelection(behaviorIndex);

// Apply specific behavior to player vehicle
RCC.SetIndividualBehaviorForPlayer(behaviorIndex);

// Check if behavior is selected in multiple selection
bool isSelected = RCC_Settings.Instance.IsBehaviorSelectedInMultiple(behaviorIndex);
```

### For Individual Vehicles
```csharp
// Get vehicle component
RCC_CarControllerV4 vehicle = GetComponent<RCC_CarControllerV4>();

// Set individual behavior for this vehicle
vehicle.SetIndividualBehaviorIndex(behaviorIndex);

// Enable individual behavior mode
vehicle.useIndividualBehavior = true;
vehicle.individualBehaviorIndex = behaviorIndex;
```

## UI Components

### RCC_UI_BehaviorSelector
Complete behavior selection interface with:
- Toggle between single/multiple selection modes
- Visual feedback for selected behaviors
- Apply button to set behavior for player vehicle

### RCC_UI_BehaviorButton
Individual behavior buttons that:
- Display behavior names
- Show selection state visually
- Handle click events for selection

### RCC_MultipleBehaviorDemo
Demo script showing:
- How to setup behavior buttons
- Mode switching functionality
- Status display and feedback

## Integration with Existing Systems

### Dashboard Integration
- Added `BehaviorSelector` button type to `RCC_UI_DashboardButton`
- Can be used to toggle behavior selector UI visibility

### Settings Integration
- New properties in `RCC_Settings`:
  - `useMultipleBehaviorSelection`: Enable/disable multiple selection
  - `multipleBehaviorSelectedIndices`: Array of selected behavior indices
  - Helper methods for managing multiple selections

### Vehicle Integration
- New properties in `RCC_CarControllerV4`:
  - `useIndividualBehavior`: Enable individual behavior for this vehicle
  - `individualBehaviorIndex`: Specific behavior index for this vehicle
  - `SetIndividualBehaviorIndex()`: Method to set individual behavior

## Backward Compatibility
- All existing single behavior selection functionality remains unchanged
- Default mode is single selection (existing behavior)
- Multiple selection is opt-in via settings

## Example Usage Scenarios

1. **Racing Game**: Select "Sport" and "Drift" behaviors, apply "Sport" to race cars and "Drift" to drift cars
2. **Simulation**: Select multiple realistic behaviors, let players choose their preferred driving style
3. **Arcade Game**: Select "Arcade" and "Easy" behaviors, apply based on difficulty level

## Notes
- When using multiple selection, the first selected behavior is applied by default
- Individual vehicle behaviors override global settings
- UI components automatically update when behavior selections change
- All changes are saved in RCC Settings asset
