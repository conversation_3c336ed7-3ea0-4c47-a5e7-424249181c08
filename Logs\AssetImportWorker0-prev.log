Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/My Project/Driving Simulator Game Z TEC
-logFile
Logs/AssetImportWorker0.log
-srvPort
53559
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Driving Simulator Game Z TEC
D:/My Project/Driving Simulator Game Z TEC
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [31008]  Target information:

Player connection [31008]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2177543972 [EditorId] 2177543972 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [31008]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2177543972 [EditorId] 2177543972 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [31008] Host joined multi-casting on [***********:54997]...
Player connection [31008] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.47 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Driving Simulator Game Z TEC/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56544
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.009363 seconds.
- Loaded All Assemblies, in  0.726 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 485 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.135 seconds
Domain Reload Profiling: 1856ms
	BeginReloadAssembly (252ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (96ms)
	LoadAllAssembliesAndSetupDomain (286ms)
		LoadAssemblies (248ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (280ms)
			TypeCache.Refresh (278ms)
				TypeCache.ScanAssembly (248ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1136ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1077ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (637ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (99ms)
			ProcessInitializeOnLoadAttributes (223ms)
			ProcessInitializeOnLoadMethodAttributes (113ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.526 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.23 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.297 seconds
Domain Reload Profiling: 2818ms
	BeginReloadAssembly (297ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (1088ms)
		LoadAssemblies (697ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (566ms)
			TypeCache.Refresh (418ms)
				TypeCache.ScanAssembly (378ms)
			BuildScriptInfoCaches (124ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1297ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1082ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (208ms)
			ProcessInitializeOnLoadAttributes (735ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 1.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 143 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6405 unused Assets / (5.7 MB). Loaded Objects now: 6987.
Memory consumption went from 243.0 MB to 237.3 MB.
Total: 33.450200 ms (FindLiveObjects: 3.675100 ms CreateObjectMapping: 3.518900 ms MarkObjects: 16.761700 ms  DeleteObjects: 9.487100 ms)

========================================================================
Received Import Request.
  Time since last request: 23951.513953 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Shared/PhysicsMaterials/Sand.physicMaterial
  artifactKey: Guid(ee4bb0d1ce749cf44ac6dc2a56ad59f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Shared/PhysicsMaterials/Sand.physicMaterial using Guid(ee4bb0d1ce749cf44ac6dc2a56ad59f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '309f4307ab0426c08d40d2de2224536a') in 0.0445124 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Shared/PhysicsMaterials/Stone.physicMaterial
  artifactKey: Guid(7509f6c6d640a774f9dfaa8af8658a80) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Shared/PhysicsMaterials/Stone.physicMaterial using Guid(7509f6c6d640a774f9dfaa8af8658a80) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1bc36aae9144e94379f08540f4979495') in 0.0014321 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/RealisticCarControllerV4/Physics Materials/RCC_TerrainPhysics3.physicMaterial
  artifactKey: Guid(b87561d43d3bd794696695748df84174) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RealisticCarControllerV4/Physics Materials/RCC_TerrainPhysics3.physicMaterial using Guid(b87561d43d3bd794696695748df84174) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9ed06ff15e331172f1ec9fbd5ba8947f') in 0.0011719 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.554 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.244 seconds
Domain Reload Profiling: 2798ms
	BeginReloadAssembly (381ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (1023ms)
		LoadAssemblies (795ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (422ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (371ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1244ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1021ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (197ms)
			ProcessInitializeOnLoadAttributes (684ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Refreshing native plugins compatible for Editor in 42.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6403 unused Assets / (6.7 MB). Loaded Objects now: 6992.
Memory consumption went from 221.0 MB to 214.3 MB.
Total: 56.272000 ms (FindLiveObjects: 2.013800 ms CreateObjectMapping: 3.747400 ms MarkObjects: 32.407600 ms  DeleteObjects: 18.099600 ms)

Prepare: number of updated asset objects reloaded= 0
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0