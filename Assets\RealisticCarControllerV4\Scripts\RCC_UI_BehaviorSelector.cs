//----------------------------------------------
//            Realistic Car Controller
//
// Copyright © 2014 - 2024 BoneCracker Games
// https://www.bonecrackergames.com
// <PERSON><PERSON><PERSON>
//
//----------------------------------------------

using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// UI component for multiple behavior selection in RCC.
/// </summary>
public class RCC_UI_BehaviorSelector : RCC_Core {

    [Header("UI References")]
    public Transform behaviorButtonsParent;     // Parent transform for behavior buttons
    public GameObject behaviorButtonPrefab;    // Prefab for behavior buttons
    public Button toggleMultipleSelectionButton;   // Button to toggle multiple selection mode
    public Text multipleSelectionStatusText;   // Text to show current selection mode
    public Button applyToPlayerButton;         // Button to apply selected behavior to player vehicle

    [Header("Settings")]
    public bool useMultipleSelection = false;  // Current selection mode
    
    private List<RCC_UI_BehaviorButton> behaviorButtons = new List<RCC_UI_BehaviorButton>();
    private int currentIndividualSelection = -1;   // Currently selected individual behavior

    private void Start() {

        CreateBehaviorButtons();
        UpdateUI();

        // Setup button events
        if (toggleMultipleSelectionButton)
            toggleMultipleSelectionButton.onClick.AddListener(ToggleMultipleSelection);

        if (applyToPlayerButton)
            applyToPlayerButton.onClick.AddListener(ApplySelectedBehaviorToPlayer);

    }

    /// <summary>
    /// Creates behavior buttons based on available behaviors in RCC Settings.
    /// </summary>
    private void CreateBehaviorButtons() {

        if (!behaviorButtonsParent || !behaviorButtonPrefab)
            return;

        // Clear existing buttons
        foreach (Transform child in behaviorButtonsParent) {
            if (Application.isPlaying)
                Destroy(child.gameObject);
            else
                DestroyImmediate(child.gameObject);
        }

        behaviorButtons.Clear();

        // Create buttons for each behavior
        for (int i = 0; i < Settings.behaviorTypes.Length; i++) {

            GameObject buttonObj = Instantiate(behaviorButtonPrefab, behaviorButtonsParent);
            RCC_UI_BehaviorButton behaviorButton = buttonObj.GetComponent<RCC_UI_BehaviorButton>();

            if (behaviorButton == null)
                behaviorButton = buttonObj.AddComponent<RCC_UI_BehaviorButton>();

            behaviorButton.Initialize(i, Settings.behaviorTypes[i].behaviorName, this);
            behaviorButtons.Add(behaviorButton);

        }

    }

    /// <summary>
    /// Toggles between single and multiple selection mode.
    /// </summary>
    public void ToggleMultipleSelection() {

        useMultipleSelection = !useMultipleSelection;
        Settings.useMultipleBehaviorSelection = useMultipleSelection;

        // Clear current selections when switching modes
        if (useMultipleSelection) {
            // Clear single selection
            currentIndividualSelection = -1;
        } else {
            // Clear multiple selections
            Settings.multipleBehaviorSelectedIndices = new int[0];
        }

        UpdateUI();

    }

    /// <summary>
    /// Called when a behavior button is clicked.
    /// </summary>
    public void OnBehaviorButtonClicked(int behaviorIndex) {

        if (useMultipleSelection) {
            // Multiple selection mode
            if (Settings.IsBehaviorSelectedInMultiple(behaviorIndex)) {
                RCC.RemoveBehaviorFromMultipleSelection(behaviorIndex);
            } else {
                RCC.AddBehaviorToMultipleSelection(behaviorIndex);
            }
        } else {
            // Single selection mode
            currentIndividualSelection = behaviorIndex;
            RCC.SetBehavior(behaviorIndex);
        }

        UpdateUI();

    }

    /// <summary>
    /// Applies the currently selected behavior to the player vehicle.
    /// </summary>
    public void ApplySelectedBehaviorToPlayer() {

        if (useMultipleSelection) {
            // In multiple selection mode, apply the first selected behavior to player
            if (Settings.multipleBehaviorSelectedIndices.Length > 0) {
                RCC.SetIndividualBehaviorForPlayer(Settings.multipleBehaviorSelectedIndices[0]);
            }
        } else {
            // In single selection mode, apply the selected behavior to player
            if (currentIndividualSelection >= 0) {
                RCC.SetIndividualBehaviorForPlayer(currentIndividualSelection);
            }
        }

    }

    /// <summary>
    /// Updates the UI to reflect current selection state.
    /// </summary>
    private void UpdateUI() {

        // Update selection mode text
        if (multipleSelectionStatusText) {
            multipleSelectionStatusText.text = useMultipleSelection ? "Multiple Selection Mode" : "Single Selection Mode";
        }

        // Update behavior buttons
        for (int i = 0; i < behaviorButtons.Count; i++) {

            if (behaviorButtons[i] != null) {

                bool isSelected = false;

                if (useMultipleSelection) {
                    isSelected = Settings.IsBehaviorSelectedInMultiple(i);
                } else {
                    isSelected = (currentIndividualSelection == i);
                }

                behaviorButtons[i].SetSelected(isSelected);

            }

        }

        // Update apply button
        if (applyToPlayerButton) {
            bool canApply = false;

            if (useMultipleSelection) {
                canApply = Settings.multipleBehaviorSelectedIndices.Length > 0;
            } else {
                canApply = currentIndividualSelection >= 0;
            }

            applyToPlayerButton.interactable = canApply;
        }

    }

    private void OnEnable() {

        // Listen for behavior changes
        RCC_SceneManager.OnBehaviorChanged += UpdateUI;

    }

    private void OnDisable() {

        // Stop listening for behavior changes
        RCC_SceneManager.OnBehaviorChanged -= UpdateUI;

    }

}
