//----------------------------------------------
//            Realistic Car Controller
//
// Copyright © 2014 - 2024 BoneCracker Games
// https://www.bonecrackergames.com
// <PERSON><PERSON><PERSON>
//
//----------------------------------------------

using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Test script to verify individual behavior functionality.
/// Behavior System Developed by <PERSON> Taj
/// </summary>
public class RCC_BehaviorTester : RCC_Core {

    [Header("Test Settings")]
    public RCC_CarControllerV4 testVehicle;     // Vehicle to test
    public Text statusText;                     // UI text to show status
    public Button[] behaviorTestButtons;        // Buttons to test different behaviors
    
    [Header("Debug Options")]
    public bool showDebugInfo = true;           // Show debug information
    public bool logWheelFrictions = true;      // Log wheel friction values
    
    private void Start() {

        if (!testVehicle)
            testVehicle = FindObjectOfType<RCC_CarControllerV4>();

        SetupTestButtons();
        UpdateStatus();

    }

    /// <summary>
    /// Sets up test buttons for each behavior.
    /// </summary>
    private void SetupTestButtons() {

        if (behaviorTestButtons == null || Settings.behaviorTypes == null)
            return;

        for (int i = 0; i < behaviorTestButtons.Length && i < Settings.behaviorTypes.Length; i++) {

            int behaviorIndex = i; // Capture for closure
            
            if (behaviorTestButtons[i] != null) {
                
                // Set button text
                Text buttonText = behaviorTestButtons[i].GetComponentInChildren<Text>();
                if (buttonText)
                    buttonText.text = $"Test {Settings.behaviorTypes[i].behaviorName}";

                // Add click listener
                behaviorTestButtons[i].onClick.RemoveAllListeners();
                behaviorTestButtons[i].onClick.AddListener(() => TestBehavior(behaviorIndex));

            }

        }

    }

    /// <summary>
    /// Tests a specific behavior on the vehicle.
    /// </summary>
    public void TestBehavior(int behaviorIndex) {

        if (!testVehicle || behaviorIndex < 0 || behaviorIndex >= Settings.behaviorTypes.Length) {
            Debug.LogError("Cannot test behavior: Invalid vehicle or behavior index");
            return;
        }

        Debug.Log($"Testing behavior: {Settings.behaviorTypes[behaviorIndex].behaviorName}");

        // Apply individual behavior
        testVehicle.useIndividualBehavior = true;
        testVehicle.individualBehaviorIndex = behaviorIndex;
        testVehicle.overrideBehavior = false;

        // Force apply the behavior
        testVehicle.SetIndividualBehaviorIndex(behaviorIndex);

        // Log wheel friction values if enabled
        if (logWheelFrictions)
            LogWheelFrictions();

        UpdateStatus();

    }

    /// <summary>
    /// Logs current wheel friction values for debugging.
    /// </summary>
    private void LogWheelFrictions() {

        if (!testVehicle || testVehicle.AllWheelColliders == null)
            return;

        Debug.Log("=== Wheel Friction Values ===");

        for (int i = 0; i < testVehicle.AllWheelColliders.Length; i++) {

            if (testVehicle.AllWheelColliders[i] != null && testVehicle.AllWheelColliders[i].WheelCollider != null) {

                WheelCollider wc = testVehicle.AllWheelColliders[i].WheelCollider;
                WheelFrictionCurve forward = wc.forwardFriction;
                WheelFrictionCurve sideways = wc.sidewaysFriction;

                Debug.Log($"Wheel {i} ({testVehicle.AllWheelColliders[i].name}):");
                Debug.Log($"  Forward - Extremum: {forward.extremumValue:F2}, Asymptote: {forward.asymptoteValue:F2}");
                Debug.Log($"  Sideways - Extremum: {sideways.extremumValue:F2}, Asymptote: {sideways.asymptoteValue:F2}");

            }

        }

    }

    /// <summary>
    /// Updates the status display.
    /// </summary>
    private void UpdateStatus() {

        if (!statusText || !testVehicle)
            return;

        string status = "=== Behavior Test Status ===\n";

        if (testVehicle.useIndividualBehavior && testVehicle.individualBehaviorIndex >= 0) {
            
            if (testVehicle.individualBehaviorIndex < Settings.behaviorTypes.Length) {
                status += $"Individual Behavior: {Settings.behaviorTypes[testVehicle.individualBehaviorIndex].behaviorName}\n";
                status += $"Behavior Index: {testVehicle.individualBehaviorIndex}\n";
            } else {
                status += "Individual Behavior: INVALID INDEX\n";
            }

        } else {
            status += "Individual Behavior: DISABLED\n";
        }

        status += $"Override Behavior: {testVehicle.overrideBehavior}\n";

        // Show current behavior settings
        if (testVehicle.useIndividualBehavior && testVehicle.individualBehaviorIndex >= 0 && testVehicle.individualBehaviorIndex < Settings.behaviorTypes.Length) {
            
            var behavior = Settings.behaviorTypes[testVehicle.individualBehaviorIndex];
            status += "\n=== Current Behavior Settings ===\n";
            status += $"Steering Helper: {behavior.steeringHelper}\n";
            status += $"Traction Helper: {behavior.tractionHelper}\n";
            status += $"ABS: {behavior.ABS}\n";
            status += $"ESP: {behavior.ESP}\n";
            status += $"TCS: {behavior.TCS}\n";
            status += $"Forward Extremum: {behavior.forwardExtremumValue:F2}\n";
            status += $"Sideways Extremum: {behavior.sidewaysExtremumValue:F2}\n";

        }

        statusText.text = status;

    }

    /// <summary>
    /// Resets vehicle to global behavior.
    /// </summary>
    public void ResetToGlobalBehavior() {

        if (!testVehicle)
            return;

        testVehicle.useIndividualBehavior = false;
        testVehicle.individualBehaviorIndex = -1;
        testVehicle.overrideBehavior = false;

        Debug.Log("Reset vehicle to global behavior settings");
        UpdateStatus();

    }

    /// <summary>
    /// Tests all behaviors sequentially.
    /// </summary>
    public void TestAllBehaviors() {

        if (!testVehicle || Settings.behaviorTypes == null)
            return;

        StartCoroutine(TestAllBehaviorsCoroutine());

    }

    /// <summary>
    /// Coroutine to test all behaviors with delays.
    /// </summary>
    private System.Collections.IEnumerator TestAllBehaviorsCoroutine() {

        for (int i = 0; i < Settings.behaviorTypes.Length; i++) {

            Debug.Log($"Auto-testing behavior {i}: {Settings.behaviorTypes[i].behaviorName}");
            TestBehavior(i);
            
            yield return new WaitForSeconds(2f); // Wait 2 seconds between tests

        }

        Debug.Log("All behaviors tested!");

    }

    private void Update() {

        if (showDebugInfo && Input.GetKeyDown(KeyCode.B)) {
            if (testVehicle)
                testVehicle.DebugCurrentBehavior();
        }

        if (Input.GetKeyDown(KeyCode.U)) {
            UpdateStatus();
        }

    }

}
