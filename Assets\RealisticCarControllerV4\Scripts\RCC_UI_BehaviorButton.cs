//----------------------------------------------
//            Realistic Car Controller
//
// Copyright © 2014 - 2024 BoneCracker Games
// https://www.bonecrackergames.com
// <PERSON><PERSON><PERSON>
//
//----------------------------------------------

using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

/// <summary>
/// Individual behavior button for the behavior selector.
/// </summary>
public class RCC_UI_BehaviorButton : RCC_Core, IPointerClickHandler {

    [Header("UI References")]
    public Text behaviorNameText;       // Text component to display behavior name
    public Image backgroundImage;       // Background image for selection state
    public Button button;               // Button component

    [Header("Visual Settings")]
    public Color normalColor = Color.white;        // Normal state color
    public Color selectedColor = Color.green;      // Selected state color
    public Color hoverColor = Color.yellow;        // Hover state color

    private int behaviorIndex = -1;                 // Index of this behavior
    private string behaviorName = "";               // Name of this behavior
    private RCC_UI_BehaviorSelector parentSelector; // Reference to parent selector
    private bool isSelected = false;                // Current selection state

    /// <summary>
    /// Initializes the behavior button.
    /// </summary>
    public void Initialize(int index, string name, RCC_UI_BehaviorSelector selector) {

        behaviorIndex = index;
        behaviorName = name;
        parentSelector = selector;

        // Setup UI
        if (behaviorNameText)
            behaviorNameText.text = behaviorName;

        if (button == null)
            button = GetComponent<Button>();

        if (backgroundImage == null)
            backgroundImage = GetComponent<Image>();

        // Setup button click event
        if (button)
            button.onClick.AddListener(OnButtonClicked);

        SetSelected(false);

    }

    /// <summary>
    /// Called when the button is clicked.
    /// </summary>
    public void OnButtonClicked() {

        if (parentSelector != null) {
            parentSelector.OnBehaviorButtonClicked(behaviorIndex);
        }

    }

    /// <summary>
    /// Handles pointer click events.
    /// </summary>
    public void OnPointerClick(PointerEventData eventData) {

        OnButtonClicked();

    }

    /// <summary>
    /// Sets the selection state of this button.
    /// </summary>
    public void SetSelected(bool selected) {

        isSelected = selected;
        UpdateVisuals();

    }

    /// <summary>
    /// Updates the visual appearance based on current state.
    /// </summary>
    private void UpdateVisuals() {

        if (backgroundImage) {

            Color targetColor = isSelected ? selectedColor : normalColor;
            backgroundImage.color = targetColor;

        }

        // Update text color if needed
        if (behaviorNameText) {

            Color textColor = isSelected ? Color.white : Color.black;
            behaviorNameText.color = textColor;

        }

    }

    /// <summary>
    /// Gets the behavior index.
    /// </summary>
    public int GetBehaviorIndex() {

        return behaviorIndex;

    }

    /// <summary>
    /// Gets the behavior name.
    /// </summary>
    public string GetBehaviorName() {

        return behaviorName;

    }

    /// <summary>
    /// Gets the selection state.
    /// </summary>
    public bool IsSelected() {

        return isSelected;

    }

}
