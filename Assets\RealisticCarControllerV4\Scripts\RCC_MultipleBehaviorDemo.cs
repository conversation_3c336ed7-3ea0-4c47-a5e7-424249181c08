//----------------------------------------------
//            Realistic Car Controller
//
// Copyright © 2014 - 2024 BoneCracker Games
// https://www.bonecrackergames.com
// <PERSON><PERSON><PERSON>
//
//----------------------------------------------

using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Demo script showing how to use the new multiple behavior selection system.
/// </summary>
public class RCC_MultipleBehaviorDemo : RCC_Core {

    [Header("UI References")]
    public Button[] behaviorButtons;            // Array of behavior buttons
    public Text statusText;                     // Status text to show current selection
    public Button toggleModeButton;             // Button to toggle between single/multiple mode
    public Button applyToPlayerButton;          // Button to apply behavior to player

    [Head<PERSON>("Settings")]
    public bool useMultipleSelection = false;   // Current selection mode

    private void Start() {

        SetupButtons();
        UpdateUI();

    }

    /// <summary>
    /// Sets up the behavior buttons.
    /// </summary>
    private void SetupButtons() {

        // Setup individual behavior buttons
        for (int i = 0; i < behaviorButtons.Length && i < Settings.behaviorTypes.Length; i++) {

            int behaviorIndex = i; // Capture for closure
            
            if (behaviorButtons[i] != null) {
                
                // Set button text to behavior name
                Text buttonText = behaviorButtons[i].GetComponentInChildren<Text>();
                if (buttonText)
                    buttonText.text = Settings.behaviorTypes[i].behaviorName;

                // Add click listener
                behaviorButtons[i].onClick.AddListener(() => OnBehaviorButtonClicked(behaviorIndex));

            }

        }

        // Setup toggle mode button
        if (toggleModeButton) {
            toggleModeButton.onClick.AddListener(ToggleSelectionMode);
        }

        // Setup apply to player button
        if (applyToPlayerButton) {
            applyToPlayerButton.onClick.AddListener(ApplyToPlayer);
        }

    }

    /// <summary>
    /// Called when a behavior button is clicked.
    /// </summary>
    private void OnBehaviorButtonClicked(int behaviorIndex) {

        if (useMultipleSelection) {
            
            // Multiple selection mode
            if (Settings.IsBehaviorSelectedInMultiple(behaviorIndex)) {
                RCC.RemoveBehaviorFromMultipleSelection(behaviorIndex);
            } else {
                RCC.AddBehaviorToMultipleSelection(behaviorIndex);
            }

        } else {
            
            // Single selection mode
            RCC.SetBehavior(behaviorIndex);

        }

        UpdateUI();

    }

    /// <summary>
    /// Toggles between single and multiple selection mode.
    /// </summary>
    private void ToggleSelectionMode() {

        useMultipleSelection = !useMultipleSelection;
        Settings.useMultipleBehaviorSelection = useMultipleSelection;

        // Clear selections when switching modes
        if (useMultipleSelection) {
            // Switching to multiple mode - clear single selection
            Settings.behaviorSelectedIndex = 0;
        } else {
            // Switching to single mode - clear multiple selections
            Settings.multipleBehaviorSelectedIndices = new int[0];
        }

        UpdateUI();

    }

    /// <summary>
    /// Applies selected behavior to player vehicle.
    /// </summary>
    private void ApplyToPlayer() {

        if (useMultipleSelection) {
            
            // Apply first selected behavior from multiple selection
            if (Settings.multipleBehaviorSelectedIndices.Length > 0) {
                RCC.SetIndividualBehaviorForPlayer(Settings.multipleBehaviorSelectedIndices[0]);
            }

        } else {
            
            // Apply single selected behavior
            RCC.SetIndividualBehaviorForPlayer(Settings.behaviorSelectedIndex);

        }

    }

    /// <summary>
    /// Updates the UI to reflect current state.
    /// </summary>
    private void UpdateUI() {

        // Update behavior buttons
        for (int i = 0; i < behaviorButtons.Length && i < Settings.behaviorTypes.Length; i++) {

            if (behaviorButtons[i] != null) {

                bool isSelected = false;
                ColorBlock colors = behaviorButtons[i].colors;

                if (useMultipleSelection) {
                    isSelected = Settings.IsBehaviorSelectedInMultiple(i);
                } else {
                    isSelected = (Settings.behaviorSelectedIndex == i);
                }

                // Update button color based on selection
                if (isSelected) {
                    colors.normalColor = Color.green;
                    colors.highlightedColor = Color.green * 0.8f;
                } else {
                    colors.normalColor = Color.white;
                    colors.highlightedColor = Color.gray;
                }

                behaviorButtons[i].colors = colors;

            }

        }

        // Update status text
        if (statusText) {

            string status = useMultipleSelection ? "Multiple Selection Mode\n" : "Single Selection Mode\n";

            if (useMultipleSelection) {
                status += "Selected: ";
                for (int i = 0; i < Settings.multipleBehaviorSelectedIndices.Length; i++) {
                    int index = Settings.multipleBehaviorSelectedIndices[i];
                    if (index >= 0 && index < Settings.behaviorTypes.Length) {
                        status += Settings.behaviorTypes[index].behaviorName;
                        if (i < Settings.multipleBehaviorSelectedIndices.Length - 1)
                            status += ", ";
                    }
                }
            } else {
                if (Settings.behaviorSelectedIndex >= 0 && Settings.behaviorSelectedIndex < Settings.behaviorTypes.Length) {
                    status += "Selected: " + Settings.behaviorTypes[Settings.behaviorSelectedIndex].behaviorName;
                }
            }

            statusText.text = status;

        }

        // Update toggle button text
        if (toggleModeButton) {
            Text buttonText = toggleModeButton.GetComponentInChildren<Text>();
            if (buttonText) {
                buttonText.text = useMultipleSelection ? "Switch to Single" : "Switch to Multiple";
            }
        }

    }

    private void OnEnable() {

        // Listen for behavior changes
        RCC_SceneManager.OnBehaviorChanged += UpdateUI;

    }

    private void OnDisable() {

        // Stop listening for behavior changes
        RCC_SceneManager.OnBehaviorChanged -= UpdateUI;

    }

}
